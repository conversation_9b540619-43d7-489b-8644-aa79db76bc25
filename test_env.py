import torch
import sys

print('Python version:', sys.version)
print('PyTorch version:', torch.__version__)
print('CUDA available:', torch.cuda.is_available())
if torch.cuda.is_available():
    print('CUDA version:', torch.version.cuda)
    print('GPU count:', torch.cuda.device_count())
    print('Current GPU:', torch.cuda.current_device())
    print('GPU name:', torch.cuda.get_device_name(0))
else:
    print('CUDA version: N/A')
    print('GPU count: 0')
