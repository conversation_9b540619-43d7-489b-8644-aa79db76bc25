#!/usr/bin/env python3
"""
命令行參數解析器，用於手動選擇攻擊方法和聚合方法
"""

import argparse
from typing import List, Dict, Any
import sys

# 可用的聚合方法
AVAILABLE_AGGREGATORS = {
    'fedavg': 'FedAvgAggregator',
    'rkd': 'RKDAggregator', 
    'flame': 'FLAMEAggregator',
    'feddf': 'FedDFAggregator',
    'fedrad': 'FedRADAggregator',
    'fedbe': 'FedBEAggregator',
    'rlr': 'RLRAggregator',
    'foolsgold': 'FoolsgoldAggregator',
    'without_kd': 'without_KDAggregator'
}

# 可用的攻擊場景
AVAILABLE_ATTACKS = {
    'none': {
        'name': 'No Attacks',
        'faulty': [],
        'malicious': [],
        'free_rider': [],
        'attack_type': 'none',
        'description': '無攻擊基線'
    },
    # A3FL (A Little Is Enough) 攻擊
    'a3fl_20': {
        'name': '20% A3FL Attack',
        'faulty': [],
        'malicious': [],
        'free_rider': [2, 5, 8],
        'attack_type': 'a3fl',
        'description': '20% A Little Is Enough 攻擊'
    },
    'a3fl_40': {
        'name': '40% A3FL Attack',
        'faulty': [],
        'malicious': [],
        'free_rider': [2, 5, 8, 11, 14, 17],
        'attack_type': 'a3fl',
        'description': '40% A Little Is Enough 攻擊'
    },
    'a3fl_60': {
        'name': '60% A3FL Attack',
        'faulty': [],
        'malicious': [],
        'free_rider': [2, 5, 8, 11, 14, 17, 20, 23, 26],
        'attack_type': 'a3fl',
        'description': '60% A Little Is Enough 攻擊'
    },
    # DBA (Distributed Backdoor Attack) 攻擊
    'dba_20': {
        'name': '20% DBA Attack',
        'faulty': [],
        'malicious': [2, 5, 8],
        'free_rider': [],
        'attack_type': 'dba',
        'description': '20% 分散式後門攻擊'
    },
    'dba_40': {
        'name': '40% DBA Attack',
        'faulty': [],
        'malicious': [2, 5, 8, 11, 14, 17],
        'free_rider': [],
        'attack_type': 'dba',
        'description': '40% 分散式後門攻擊'
    },
    # F3BA (Federated Backdoor Attack) 攻擊
    'f3ba_20': {
        'name': '20% F3BA Attack',
        'faulty': [],
        'malicious': [2, 5, 8],
        'free_rider': [],
        'attack_type': 'f3ba',
        'description': '20% 聯邦後門攻擊'
    },
    'f3ba_40': {
        'name': '40% F3BA Attack',
        'faulty': [],
        'malicious': [2, 5, 8, 11, 14, 17],
        'free_rider': [],
        'attack_type': 'f3ba',
        'description': '40% 聯邦後門攻擊'
    },
    # TSBA (Trojan/Steganographic Backdoor Attack) 攻擊
    'tsba_20': {
        'name': '20% TSBA Attack',
        'faulty': [],
        'malicious': [2, 5, 8],
        'free_rider': [],
        'attack_type': 'tsba',
        'description': '20% 特洛伊後門攻擊'
    },
    'tsba_40': {
        'name': '40% TSBA Attack',
        'faulty': [],
        'malicious': [2, 5, 8, 11, 14, 17],
        'free_rider': [],
        'attack_type': 'tsba',
        'description': '40% 特洛伊後門攻擊'
    },
    # 拜占庭攻擊
    'byzantine_20': {
        'name': '20% Byzantine Attack',
        'faulty': [2, 5, 8],
        'malicious': [],
        'free_rider': [],
        'attack_type': 'byzantine',
        'description': '20% 拜占庭攻擊'
    },
    'byzantine_40': {
        'name': '40% Byzantine Attack',
        'faulty': [2, 5, 8, 11, 14, 17],
        'malicious': [],
        'free_rider': [],
        'attack_type': 'byzantine',
        'description': '40% 拜占庭攻擊'
    },
    # 混合攻擊
    'mixed_attack': {
        'name': 'Mixed Attack',
        'faulty': [2, 5],
        'malicious': [8, 11],
        'free_rider': [14, 17],
        'attack_type': 'mixed',
        'description': '混合攻擊 (拜占庭 + 惡意 + 搭便車)'
    }
}

# 可用的數據集
AVAILABLE_DATASETS = {
    'cifar10': 'CIFAR-10',
    'fashionmnist': 'Fashion-MNIST',
    'emnist': 'EMNIST'
}


def create_parser() -> argparse.ArgumentParser:
    """創建命令行參數解析器"""
    parser = argparse.ArgumentParser(
        description='聯邦學習實驗 - 手動選擇攻擊方法和聚合方法',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=f"""
可用的聚合方法:
{chr(10).join([f'  {k}: {v}' for k, v in AVAILABLE_AGGREGATORS.items()])}

可用的攻擊場景:
{chr(10).join([f'  {k}: {v["description"]}' for k, v in AVAILABLE_ATTACKS.items()])}

可用的數據集:
{chr(10).join([f'  {k}: {v}' for k, v in AVAILABLE_DATASETS.items()])}

使用示例:
  python main.py --aggregator rkd --attack a3fl_20 --dataset cifar10
  python main.py --aggregator fedavg --attack none --dataset fashionmnist --rounds 50
  python main.py --list-options  # 顯示所有可用選項
        """
    )
    
    # 主要參數
    parser.add_argument(
        '--aggregator', '-a',
        type=str,
        choices=list(AVAILABLE_AGGREGATORS.keys()),
        required=False,
        help='選擇聚合方法'
    )
    
    parser.add_argument(
        '--attack', '-t',
        type=str, 
        choices=list(AVAILABLE_ATTACKS.keys()),
        required=False,
        help='選擇攻擊場景'
    )
    
    parser.add_argument(
        '--dataset', '-d',
        type=str,
        choices=list(AVAILABLE_DATASETS.keys()),
        default='cifar10',
        help='選擇數據集 (默認: cifar10)'
    )
    
    # 實驗參數
    parser.add_argument(
        '--rounds', '-r',
        type=int,
        default=30,
        help='聯邦學習輪數 (默認: 30)'
    )
    
    parser.add_argument(
        '--epochs', '-e',
        type=int,
        default=5,
        help='本地訓練輪數 (默認: 5)'
    )
    
    parser.add_argument(
        '--batch-size', '-b',
        type=int,
        default=64,
        help='批次大小 (默認: 64)'
    )
    
    parser.add_argument(
        '--learning-rate', '-lr',
        type=float,
        default=0.05,
        help='學習率 (默認: 0.05)'
    )
    
    parser.add_argument(
        '--alpha-dirichlet',
        type=float,
        default=0.3,
        help='Dirichlet 分佈參數，控制數據非IID程度 (默認: 0.3)'
    )
    
    # 輸出控制
    parser.add_argument(
        '--output-dir', '-o',
        type=str,
        default='results',
        help='結果輸出目錄 (默認: results)'
    )
    
    parser.add_argument(
        '--no-plot',
        action='store_true',
        help='不生成圖表'
    )
    
    parser.add_argument(
        '--seed',
        type=int,
        default=0,
        help='隨機種子 (默認: 0)'
    )
    
    # 工具選項
    parser.add_argument(
        '--list-options',
        action='store_true',
        help='列出所有可用的選項並退出'
    )
    
    parser.add_argument(
        '--interactive', '-i',
        action='store_true',
        help='交互式選擇模式'
    )
    
    return parser


def list_all_options():
    """列出所有可用選項"""
    print("=== 可用的聚合方法 ===")
    for key, value in AVAILABLE_AGGREGATORS.items():
        print(f"  {key:12} : {value}")
    
    print("\n=== 可用的攻擊場景 ===")
    for key, value in AVAILABLE_ATTACKS.items():
        print(f"  {key:15} : {value['description']}")
        print(f"                   故障客戶端: {value['faulty']}")
        print(f"                   惡意客戶端: {value['malicious']}")
        print(f"                   搭便車客戶端: {value['free_rider']}")
        print()
    
    print("=== 可用的數據集 ===")
    for key, value in AVAILABLE_DATASETS.items():
        print(f"  {key:12} : {value}")


def interactive_selection() -> Dict[str, Any]:
    """交互式選擇模式"""
    print("=== 交互式選擇模式 ===\n")
    
    # 選擇聚合方法
    print("可用的聚合方法:")
    for i, (key, value) in enumerate(AVAILABLE_AGGREGATORS.items(), 1):
        print(f"  {i}. {key} ({value})")
    
    while True:
        try:
            choice = input(f"\n請選擇聚合方法 (1-{len(AVAILABLE_AGGREGATORS)}): ")
            aggregator_idx = int(choice) - 1
            if 0 <= aggregator_idx < len(AVAILABLE_AGGREGATORS):
                aggregator = list(AVAILABLE_AGGREGATORS.keys())[aggregator_idx]
                break
            else:
                print("無效選擇，請重新輸入")
        except ValueError:
            print("請輸入數字")
    
    # 選擇攻擊場景
    print("\n可用的攻擊場景:")
    for i, (key, value) in enumerate(AVAILABLE_ATTACKS.items(), 1):
        print(f"  {i}. {key} ({value['description']})")
    
    while True:
        try:
            choice = input(f"\n請選擇攻擊場景 (1-{len(AVAILABLE_ATTACKS)}): ")
            attack_idx = int(choice) - 1
            if 0 <= attack_idx < len(AVAILABLE_ATTACKS):
                attack = list(AVAILABLE_ATTACKS.keys())[attack_idx]
                break
            else:
                print("無效選擇，請重新輸入")
        except ValueError:
            print("請輸入數字")
    
    # 選擇數據集
    print("\n可用的數據集:")
    for i, (key, value) in enumerate(AVAILABLE_DATASETS.items(), 1):
        print(f"  {i}. {key} ({value})")
    
    while True:
        try:
            choice = input(f"\n請選擇數據集 (1-{len(AVAILABLE_DATASETS)}, 默認: 1): ") or "1"
            dataset_idx = int(choice) - 1
            if 0 <= dataset_idx < len(AVAILABLE_DATASETS):
                dataset = list(AVAILABLE_DATASETS.keys())[dataset_idx]
                break
            else:
                print("無效選擇，請重新輸入")
        except ValueError:
            print("請輸入數字")
    
    return {
        'aggregator': aggregator,
        'attack': attack,
        'dataset': dataset
    }


def validate_args(args) -> bool:
    """驗證參數有效性"""
    if args.list_options:
        return True

    if args.interactive:
        return True

    if not args.aggregator:
        print("錯誤: 必須指定聚合方法 (--aggregator)")
        print("使用 --list-options 查看可用選項")
        return False

    if not args.attack:
        print("錯誤: 必須指定攻擊場景 (--attack)")
        print("使用 --list-options 查看可用選項")
        return False

    # 驗證聚合器名稱
    if args.aggregator not in AVAILABLE_AGGREGATORS:
        print(f"錯誤: 無效的聚合方法 '{args.aggregator}'")
        print(f"可用選項: {', '.join(AVAILABLE_AGGREGATORS.keys())}")
        return False

    # 驗證攻擊場景名稱
    if args.attack not in AVAILABLE_ATTACKS:
        print(f"錯誤: 無效的攻擊場景 '{args.attack}'")
        print(f"可用選項: {', '.join(AVAILABLE_ATTACKS.keys())}")
        return False

    # 驗證數據集名稱
    if args.dataset not in AVAILABLE_DATASETS:
        print(f"錯誤: 無效的數據集 '{args.dataset}'")
        print(f"可用選項: {', '.join(AVAILABLE_DATASETS.keys())}")
        return False

    # 驗證數值參數
    if args.rounds <= 0:
        print("錯誤: 輪數必須大於 0")
        return False

    if args.epochs <= 0:
        print("錯誤: 本地訓練輪數必須大於 0")
        return False

    if args.batch_size <= 0:
        print("錯誤: 批次大小必須大於 0")
        return False

    if args.learning_rate <= 0:
        print("錯誤: 學習率必須大於 0")
        return False

    if args.alpha_dirichlet <= 0:
        print("錯誤: Alpha Dirichlet 參數必須大於 0")
        return False

    return True


def parse_arguments() -> Dict[str, Any]:
    """解析命令行參數"""
    parser = create_parser()
    args = parser.parse_args()
    
    if args.list_options:
        list_all_options()
        sys.exit(0)
    
    if args.interactive:
        interactive_choices = interactive_selection()
        args.aggregator = interactive_choices['aggregator']
        args.attack = interactive_choices['attack']
        args.dataset = interactive_choices['dataset']
    
    if not validate_args(args):
        parser.print_help()
        sys.exit(1)
    
    return vars(args)


if __name__ == "__main__":
    # 測試參數解析器
    args = parse_arguments()
    print("解析的參數:", args)
