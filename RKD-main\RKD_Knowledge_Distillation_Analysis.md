# RKD (Robust Knowledge Distillation) 知識蒸餾詳細分析

## 1. 代碼定位

### 主要文件和類

**核心實現文件**：
- `utils/KnowledgeDistiller.py` - 知識蒸餾的核心實現
- `aggregators/RKD.py` - RKD 聚合器，調用知識蒸餾

**主要類名**：
- `KnowledgeDistiller` - 知識蒸餾器類
- `RKDAggregator` - RKD 聚合器類

**關鍵方法**：
- `KnowledgeDistiller.distillKnowledge()` - 執行知識蒸餾的主方法
- `KnowledgeDistiller._pseudolabelsFromEnsemble()` - 生成偽標籤
- `RKDAggregator.aggregate()` - 聚合過程中調用知識蒸餾

## 2. 知識蒸餾流程詳細解釋

### 2.1 完整流程概述

```
1. 惡意客戶端檢測 → 2. 良性模型選擇 → 3. 教師集成構建 → 4. 偽標籤生成 → 5. 學生模型訓練
```

### 2.2 具體實現流程

#### 步驟 1: 教師模型定義
```python
# 在 RKDAggregator.aggregate() 中
ensemble = self.median_models  # 教師模型集成（良性模型）
Scoresmodel = self._averageModel(ensemble)  # 學生模型（初始化為平均模型）
```

#### 步驟 2: 知識蒸餾器初始化
```python
kd = KnowledgeDistiller(
    self.distillationData,  # 蒸餾數據集（服務器端數據）
    method=self.pseudolabelMethod,  # 偽標籤生成方法："avglogits"
)
```

#### 步驟 3: 執行知識蒸餾
```python
avg_model = kd.distillKnowledge(ensemble, Scoresmodel)
```

### 2.3 教師模型和學生模型定義

**教師模型（Teacher Models）**：
- **來源**：`self.median_models` - 通過惡意檢測後選出的良性客戶端模型
- **數量**：通常為 7 個模型（k=7）
- **作用**：生成軟標籤（soft labels）用於指導學生模型學習

**學生模型（Student Model）**：
- **初始化**：`self._averageModel(ensemble)` - 教師模型的平均
- **訓練目標**：學習教師集成的知識
- **最終輸出**：經過知識蒸餾訓練的全局模型

### 2.4 偽標籤生成過程

#### 方法選擇
```python
self.pseudolabelMethod = "avglogits"  # 默認方法
```

#### 具體實現
```python
def _pseudolabelsFromEnsemble(self, ensemble, method=None):
    with torch.no_grad():
        dataLoader = DataLoader(self.dataset, batch_size=self.batch_size)
        preds = []
        for i, (x, y) in enumerate(dataLoader):
            # 關鍵：使用溫度參數 T 縮放 logits
            predsBatch = torch.stack([m(x) / self.T for m in ensemble])
            preds.append(predsBatch)
        preds = torch.cat(preds, dim=1)
        
        if method == "avglogits":
            pseudolabels = preds.mean(dim=0)  # 平均 logits
            return F.softmax(pseudolabels, dim=1)  # 轉換為概率分佈
        elif method == "medlogits":
            pseudolabels, idx = preds.median(dim=0)  # 中位數 logits
            return F.softmax(pseudolabels, dim=1)
```

## 3. 溫度參數 T 的詳細分析

### 3.1 溫度參數的數學原理

**Softmax 溫度縮放公式**：
```
P(y_i|x) = exp(z_i/T) / Σ_j exp(z_j/T)
```

其中：
- `z_i` 是第 i 類的 logit 值
- `T` 是溫度參數
- `T > 1` 使分佈更平滑（軟化）
- `T < 1` 使分佈更尖銳
- `T = 1` 是標準 softmax

### 3.2 代碼中的溫度參數設定

#### 默認值設定
```python
# KnowledgeDistiller.__init__()
def __init__(self, dataset, epoc=3, batch_size=16, temperature=2, ...):
    self.T = temperature  # 默認 T = 2
```

#### 使用位置 1: 偽標籤生成
```python
# _pseudolabelsFromEnsemble() 中
predsBatch = torch.stack([m(x) / self.T for m in ensemble])
```

#### 使用位置 2: 知識蒸餾損失計算
```python
# distillKnowledge() 中
err = loss(F.log_softmax(pred / self.T, dim=1), y) * self.T * self.T
```

### 3.3 溫度參數的作用機制

**T = 2 的影響**：
1. **軟化教師預測**：使教師模型的預測更平滑，包含更多類間關係信息
2. **知識傳遞**：幫助學生模型學習教師的"不確定性"
3. **損失縮放**：`* self.T * self.T` 補償溫度縮放對梯度的影響

**不同 T 值的效果**：
- `T = 1`：標準蒸餾，硬標籤
- `T = 2`：適度軟化，平衡性能和魯棒性
- `T > 3`：過度軟化，可能損失重要信息

## 4. 相關參數分析

### 4.1 知識蒸餾核心參數

```python
# KnowledgeDistiller 參數
self.T = 2              # 溫度參數
self.epoc = 3           # 蒸餾訓練輪數
self.batch_size = 16    # 批次大小
self.lr1 = 0.001        # 學習率
self.swa_lr = 0.01      # SWA 學習率
self.momentum = 0.9     # SGD 動量
```

### 4.2 Client 中的 alpha 和 beta 參數

```python
# Client.__init__()
self.alpha: float = 3.0  # AFA 算法參數
self.beta: float = 3.0   # AFA 算法參數
self.score: float = alpha / beta  # 客戶端評分
```

**注意**：這些 alpha/beta 參數與知識蒸餾無直接關係，主要用於 AFA (Adaptive Federated Averaging) 算法。

### 4.3 不同攻擊場景下的表現

**參數一致性**：
- 所有攻擊場景使用相同的知識蒸餾參數
- `T = 2` 在各種攻擊下都保持不變
- 蒸餾方法固定為 "avglogits"

**適應性機制**：
- 通過教師模型選擇（良性模型）實現攻擊適應
- 動態調整教師集成大小（基於檢測結果）

## 5. 關鍵代碼片段展示

### 5.1 知識蒸餾主流程
```python
def distillKnowledge(self, ensemble, Scoresmodel):
    # 生成偽標籤
    self.dataset.labels = self._pseudolabelsFromEnsemble(ensemble, self.method)
    
    # 設置優化器和損失函數
    opt = optim.SGD(Scoresmodel.parameters(), momentum=self.momentum, 
                    lr=self.lr1, weight_decay=1e-5)
    loss = nn.KLDivLoss(reduction="batchmean")
    
    # SWA (Stochastic Weight Averaging) 設置
    swa_model = AveragedModel(Scoresmodel)
    scheduler = CosineAnnealingLR(opt, T_max=100)
    swa_scheduler = SWALR(opt, swa_lr=self.swa_lr)
    
    # 訓練循環
    dataLoader = DataLoader(self.dataset, batch_size=self.batch_size)
    for i in range(self.epoc):
        total_err = 0
        for j, (x, y) in enumerate(dataLoader):
            opt.zero_grad()
            pred = Scoresmodel(x)
            
            # 關鍵：溫度縮放的 KL 散度損失
            err = loss(F.log_softmax(pred / self.T, dim=1), y) * self.T * self.T
            err.backward()
            total_err += err
            opt.step()
            
        scheduler.step()
        swa_model.update_parameters(Scoresmodel)
        swa_scheduler.step()
        torch.optim.swa_utils.update_bn(dataLoader, swa_model)
    
    return swa_model.module
```

### 5.2 溫度參數使用示例
```python
# 教師預測生成（帶溫度縮放）
predsBatch = torch.stack([m(x) / self.T for m in ensemble])

# 學生模型損失計算（帶溫度補償）
err = loss(F.log_softmax(pred / self.T, dim=1), y) * self.T * self.T
```

### 5.3 RKD 聚合器中的調用
```python
def aggregate(self, clients: List[Client], models: List[nn.Module]) -> nn.Module:
    # 初始化知識蒸餾器
    kd = KnowledgeDistiller(
        self.distillationData,
        method=self.pseudolabelMethod,  # "avglogits"
    )
    
    # 教師模型集成（良性模型）
    ensemble = self.median_models
    
    # 學生模型初始化
    Scoresmodel = self._averageModel(ensemble)
    
    # 執行知識蒸餾
    avg_model = kd.distillKnowledge(ensemble, Scoresmodel)
    
    return avg_model
```

## 6. 總結

### 6.1 知識蒸餾的核心特點
1. **溫度參數 T=2**：適度軟化教師預測，平衡性能和魯棒性
2. **集成教師**：使用多個良性模型作為教師，提高知識質量
3. **SWA 優化**：結合隨機權重平均，提高模型穩定性
4. **攻擊無關**：所有攻擊場景使用統一參數，通過教師選擇實現適應

### 6.2 設計優勢
- **魯棒性**：通過良性模型集成抵抗惡意影響
- **知識保持**：溫度縮放保留模型間的相對關係
- **計算效率**：固定參數避免動態調整開銷

### 6.3 潛在改進方向
- **自適應溫度**：根據攻擊類型動態調整 T 值
- **多樣化方法**：結合 avglogits 和 medlogits
- **參數優化**：基於實驗結果調整蒸餾參數
