#!/usr/bin/env python3
"""
測試手動選擇功能的腳本
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def test_argument_parser():
    """測試參數解析器"""
    print("=== 測試參數解析器 ===")
    
    # 測試列出選項
    try:
        result = subprocess.run([
            sys.executable, "argument_parser.py", "--list-options"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✓ 列出選項功能正常")
        else:
            print(f"✗ 列出選項失敗: {result.stderr}")
            return False
    except Exception as e:
        print(f"✗ 參數解析器測試失敗: {str(e)}")
        return False
    
    return True


def test_config_creation():
    """測試配置創建"""
    print("\n=== 測試配置創建 ===")
    
    try:
        from experiment.ManualConfig import ManualConfig
        
        # 測試有效配置
        config = ManualConfig(
            aggregator_name='rkd',
            attack_name='a3fl_20',
            dataset_name='cifar10',
            rounds=5,
            epochs=2
        )
        
        print("✓ 配置創建成功")
        print(f"  聚合器: {config.selected_aggregator}")
        print(f"  攻擊: {config.selected_attack}")
        print(f"  數據集: {config.selected_dataset}")
        
        # 測試無效配置
        try:
            invalid_config = ManualConfig(
                aggregator_name='invalid_agg',
                attack_name='a3fl_20',
                dataset_name='cifar10'
            )
            print("✗ 應該拒絕無效聚合器")
            return False
        except ValueError:
            print("✓ 正確拒絕無效聚合器")
        
        return True
        
    except Exception as e:
        print(f"✗ 配置創建測試失敗: {str(e)}")
        return False


def test_error_handler():
    """測試錯誤處理模塊"""
    print("\n=== 測試錯誤處理模塊 ===")
    
    try:
        from utils.error_handler import (
            validate_experiment_config, 
            validate_output_directory,
            print_system_info
        )
        
        # 測試配置驗證
        valid_config = {
            'aggregator': 'rkd',
            'attack': 'a3fl_20',
            'dataset': 'cifar10',
            'rounds': 10,
            'epochs': 5,
            'batch_size': 64,
            'learning_rate': 0.05,
            'alpha_dirichlet': 0.3
        }
        
        errors = validate_experiment_config(valid_config)
        if not errors:
            print("✓ 有效配置驗證通過")
        else:
            print(f"✗ 有效配置驗證失敗: {errors}")
            return False
        
        # 測試無效配置
        invalid_config = {
            'aggregator': 'rkd',
            'attack': 'a3fl_20',
            'dataset': 'cifar10',
            'rounds': -1,  # 無效值
            'epochs': 0,   # 無效值
        }
        
        errors = validate_experiment_config(invalid_config)
        if errors:
            print("✓ 無效配置正確被拒絕")
        else:
            print("✗ 應該拒絕無效配置")
            return False
        
        # 測試輸出目錄驗證
        test_dir = "test_output"
        try:
            validate_output_directory(test_dir)
            print("✓ 輸出目錄驗證成功")
            
            # 清理測試目錄
            import shutil
            if os.path.exists(test_dir):
                shutil.rmtree(test_dir)
        except Exception as e:
            print(f"✗ 輸出目錄驗證失敗: {str(e)}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ 錯誤處理模塊測試失敗: {str(e)}")
        return False


def test_quick_experiment():
    """測試快速實驗運行"""
    print("\n=== 測試快速實驗運行 ===")
    
    # 創建測試輸出目錄
    test_output_dir = "test_results"
    
    try:
        # 運行一個非常短的實驗
        cmd = [
            sys.executable, "main_manual.py",
            "--aggregator", "fedavg",
            "--attack", "none",
            "--dataset", "cifar10",
            "--rounds", "2",
            "--epochs", "1",
            "--batch-size", "32",
            "--output-dir", test_output_dir,
            "--no-plot"
        ]
        
        print("運行命令:", " ".join(cmd))
        print("這可能需要幾分鐘時間...")
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✓ 快速實驗運行成功")
            
            # 檢查輸出文件
            json_dir = Path(test_output_dir) / "json"
            if json_dir.exists() and any(json_dir.glob("*.json")):
                print("✓ 結果文件生成成功")
                
                # 讀取並驗證結果文件
                json_files = list(json_dir.glob("*.json"))
                if json_files:
                    with open(json_files[0], 'r') as f:
                        results = json.load(f)
                    
                    required_keys = ['errors', 'attack_success_rate', 'config']
                    if all(key in results for key in required_keys):
                        print("✓ 結果文件格式正確")
                    else:
                        print("✗ 結果文件格式不正確")
                        return False
            else:
                print("✗ 結果文件未生成")
                return False
            
            return True
        else:
            print(f"✗ 快速實驗運行失敗")
            print(f"錯誤輸出: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("✗ 實驗運行超時")
        return False
    except Exception as e:
        print(f"✗ 快速實驗測試失敗: {str(e)}")
        return False
    finally:
        # 清理測試輸出
        import shutil
        if os.path.exists(test_output_dir):
            shutil.rmtree(test_output_dir)


def test_all_combinations():
    """測試所有聚合器和攻擊的組合（僅驗證參數，不實際運行）"""
    print("\n=== 測試所有組合的參數驗證 ===")
    
    try:
        from argument_parser import AVAILABLE_AGGREGATORS, AVAILABLE_ATTACKS
        from experiment.ManualConfig import ManualConfig
        
        success_count = 0
        total_count = 0
        
        for agg_name in AVAILABLE_AGGREGATORS.keys():
            for att_name in AVAILABLE_ATTACKS.keys():
                total_count += 1
                try:
                    config = ManualConfig(
                        aggregator_name=agg_name,
                        attack_name=att_name,
                        dataset_name='cifar10',
                        rounds=2,
                        epochs=1
                    )
                    success_count += 1
                except Exception as e:
                    print(f"✗ 組合失敗: {agg_name} + {att_name} - {str(e)}")
        
        print(f"✓ 成功驗證 {success_count}/{total_count} 個組合")
        return success_count == total_count
        
    except Exception as e:
        print(f"✗ 組合測試失敗: {str(e)}")
        return False


def main():
    """主測試函數"""
    print("開始測試手動選擇功能...")
    
    tests = [
        ("參數解析器", test_argument_parser),
        ("配置創建", test_config_creation), 
        ("錯誤處理模塊", test_error_handler),
        ("所有組合驗證", test_all_combinations),
        # ("快速實驗運行", test_quick_experiment),  # 註釋掉以節省時間
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"測試: {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✓ {test_name} 測試通過")
                passed += 1
            else:
                print(f"✗ {test_name} 測試失敗")
        except Exception as e:
            print(f"✗ {test_name} 測試出錯: {str(e)}")
    
    print(f"\n{'='*50}")
    print(f"測試總結: {passed}/{total} 個測試通過")
    print('='*50)
    
    if passed == total:
        print("🎉 所有測試通過！手動選擇功能可以使用。")
        return True
    else:
        print("❌ 部分測試失敗，請檢查問題。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
