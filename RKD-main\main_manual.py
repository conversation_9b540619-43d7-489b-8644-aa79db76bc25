#!/usr/bin/env python3
"""
手動選擇攻擊方法和聚合方法的主程序
"""

import os
import sys
import time
import gc
import json
import random
import numpy as np
import torch
from torch import cuda, nn
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
from loguru import logger

# 導入自定義模塊
from argument_parser import parse_arguments, AVAILABLE_AGGREGATORS, AVAILABLE_ATTACKS
from experiment.ManualConfig import ManualConfig
from logger import logPrint
from client import Client
from utils.error_handler import (
    safe_experiment_execution, validate_system_requirements,
    validate_experiment_config, validate_output_directory,
    memory_cleanup, print_system_info, ProgressTracker,
    create_experiment_summary, setup_error_logging,
    ConfigurationError, DatasetError, AggregatorError
)

# 數據集加載器
from datasetLoaders.CIFAR10 import DatasetLoaderCIFAR10
from datasetLoaders.FashionMNIST import DatasetLoaderFashionMNIST
from datasetLoaders.EMNIST import DatasetLoaderEMNIST

# 分類器
from classifiers import CIFAR10
from classifiers import FashionMNIST
from classifiers import EMNIST

# 類型定義
from utils.typings import Errors, AttackSuccesRate
from typing import Callable, Dict, List, Tuple, Optional, Type
from datasetLoaders.DatasetInterface import DatasetInterface
from aggregators.Aggregator import Aggregator

# 全局變量
SEED = 0

# 圖表顏色和樣式
COLOURS = ["tab:purple", "tab:green", "tab:cyan", "tab:orange", "tab:brown", 
           "tab:blue", "tab:pink", "gold", "chartreuse", "saddlebrown"]
linestyles = ['-.', '--', '-', ':', '--', '-.', ':', '-.', '--', '-']
markers = ['D', 's', '^', 'd', 'o', '*', '+', 's', '^', 'd']

fontP = FontProperties()
fontP.set_size('large')


def set_random_seeds(seed=SEED) -> None:
    """設置隨機種子以確保結果可重現"""
    print(f"設置隨機種子為 {seed}")
    os.environ["PYTHONHASHSEED"] = str(seed)
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    cuda.manual_seed(seed)


def get_dataset_loader_and_classifier(dataset_name: str):
    """根據數據集名稱獲取數據加載器和分類器"""
    dataset_mapping = {
        'cifar10': (DatasetLoaderCIFAR10().getDatasets, CIFAR10.Classifier),
        'fashionmnist': (DatasetLoaderFashionMNIST().getDatasets, FashionMNIST.Classifier),
        'emnist': (DatasetLoaderEMNIST().getDatasets, EMNIST.Classifier)
    }
    
    if dataset_name not in dataset_mapping:
        raise ValueError(f"不支持的數據集: {dataset_name}")
    
    return dataset_mapping[dataset_name]


def init_clients(config: ManualConfig, 
                trainDatasets: List[DatasetInterface],
                useDifferentialPrivacy: bool) -> List[Client]:
    """初始化客戶端"""
    usersNo = config.percUsers.size(0)
    p0 = 1 / usersNo
    logPrint("創建客戶端...")
    clients: List[Client] = []
    
    for i in range(usersNo):
        # 獲取攻擊類型
        attack_config = config.get_attack_config() if config.selected_attack != 'none' else {'attack_type': 'none'}
        attack_type = attack_config.get('attack_type', 'none')

        clients.append(
            Client(
                idx=i,
                trainDataset=trainDatasets[i],
                epochs=config.epochs,
                batchSize=config.batchSize,
                learningRate=config.learningRate,
                p=p0,
                alpha=config.alpha,
                beta=config.beta,
                Loss=config.Loss,
                Optimizer=config.Optimizer,
                device=config.aggregatorConfig.device,
                useDifferentialPrivacy=useDifferentialPrivacy,
                epsilon1=config.epsilon1,
                epsilon3=config.epsilon3,
                needClip=config.needClip,
                clipValue=config.clipValue,
                needNormalization=config.needNormalization,
                releaseProportion=config.releaseProportion,
                attack_type=attack_type,  # 新增攻擊類型參數
                dataset_name=config.selected_dataset,  # 新增數據集名稱參數
            )
        )

    nTrain = sum([client.n for client in clients])
    # 根據訓練數據點數量加權更新值
    for client in clients:
        client.p = client.n / nTrain

    # 創建惡意（拜占庭）和故障用戶
    for client in clients:
        if client.id in config.faulty:
            client.byz = True
            logPrint("用戶", client.id, "是故障的。")
        if client.id in config.malicious:
            client.flip = True
            logPrint("用戶", client.id, "是惡意的。")
        if client.id in config.freeRiding:
            client.free = True
            logPrint("用戶", client.id, "是搭便車的。")

    return clients


def run_single_experiment(config: ManualConfig,
                         datasetLoader: Callable,
                         classifier: nn.Module,
                         agg: Type[Aggregator],
                         useDifferentialPrivacy: bool,
                         folder: str) -> Tuple[Errors, AttackSuccesRate]:
    """運行單個實驗"""
    serverDataSize = config.serverDataSize
    if not agg.requiresData():
        serverDataSize = 0
        
    trainDatasets, testDataset, serverDataset = datasetLoader(
        config.percUsers,
        config.labels,
        config.datasetSize,
        config.nonIID,
        config.alphaDirichlet,
        serverDataSize,
    )

    # 打印客戶端數據分區信息
    clientPartitions = torch.stack([torch.bincount(t.labels, minlength=10) for t in trainDatasets])
    
    logPrint(f"客戶端數據分區 (alpha={config.alphaDirichlet}, 服務器百分比: {100*serverDataSize:.2f}%)")
    logPrint(f"每個客戶端的數據: {clientPartitions.sum(dim=1)}")
    logPrint(f"每個客戶端每個類別的樣本數: \n{clientPartitions}")
    
    clients = init_clients(config, trainDatasets, useDifferentialPrivacy)
    
    # 需要根據數據集泛化和分類更新模型輸入大小
    if config.requireDatasetAnonymization:
        classifier.inputSize = testDataset.getInputSize()
    model = classifier().to(config.aggregatorConfig.device)
    
    aggregator = agg(clients, model, config.aggregatorConfig)

    if aggregator.requiresData():
        serverDataset.data = serverDataset.data.to(aggregator.config.device)
        serverDataset.labels = serverDataset.labels.to(aggregator.config.device)
        aggregator.distillationData = serverDataset

    errors, AttackSuccesRate = aggregator.trainAndTest(testDataset)
    
    return errors, AttackSuccesRate


def save_results(errors: Errors, 
                attack_success_rate: AttackSuccesRate,
                config: ManualConfig,
                folder: str):
    """保存實驗結果"""
    if not os.path.isdir(folder):
        os.makedirs(folder)
    if not os.path.isdir(f"{folder}/json"):
        os.mkdir(f"{folder}/json")
    if not os.path.isdir(f"{folder}/graphs"):
        os.mkdir(f"{folder}/graphs")
    
    experiment_name = config.get_experiment_name()
    
    # 保存JSON結果
    results = {
        'errors': errors.tolist(),
        'attack_success_rate': attack_success_rate.tolist(),
        'config': {
            'aggregator': config.selected_aggregator,
            'attack': config.selected_attack,
            'dataset': config.selected_dataset,
            'rounds': config.aggregatorConfig.rounds,
            'epochs': config.epochs,
            'batch_size': config.batchSize,
            'learning_rate': config.learningRate,
            'alpha_dirichlet': config.alphaDirichlet
        }
    }
    
    with open(f"{folder}/json/{experiment_name}_results(Seed_{SEED}).json", "w+") as outfile:
        json.dump(results, outfile, indent=2)
    
    logPrint(f"結果已保存到 {folder}/json/{experiment_name}_results(Seed_{SEED}).json")


def plot_results(errors: Errors,
                attack_success_rate: AttackSuccesRate, 
                config: ManualConfig,
                folder: str):
    """繪製結果圖表"""
    if not config.plotResults:
        return
        
    experiment_name = config.get_experiment_name()
    
    # 準確率圖表
    plt.figure(figsize=(10, 6))
    plt.plot(errors, color=COLOURS[0], linestyle=linestyles[0], marker=markers[0])
    plt.xlabel("輪數")
    plt.ylabel("準確率 (%)")
    plt.title(f"{config.selected_aggregator.upper()} - {config.selected_attack} - 準確率", loc="center")
    plt.ylim([-0.05, 1.05])
    plt.grid(True, alpha=0.3)
    plt.savefig(f"{folder}/graphs/{experiment_name}_accuracy.pdf", bbox_inches="tight", pad_inches=0.1)
    plt.close()

    # 攻擊成功率圖表
    plt.figure(figsize=(10, 6))
    plt.plot(attack_success_rate, color=COLOURS[1], linestyle=linestyles[1], marker=markers[1])
    plt.xlabel("輪數")
    plt.ylabel("攻擊成功率 (%)")
    plt.title(f"{config.selected_aggregator.upper()} - {config.selected_attack} - 攻擊成功率", loc="center")
    plt.ylim([-0.05, 1.05])
    plt.grid(True, alpha=0.3)
    plt.savefig(f"{folder}/graphs/{experiment_name}_attack_success_rate.pdf", bbox_inches="tight", pad_inches=0.1)
    plt.close()
    
    logPrint(f"圖表已保存到 {folder}/graphs/")


def main():
    """主函數"""
    # 設置錯誤日誌
    setup_error_logging()

    # 解析命令行參數
    args = parse_arguments()

    # 打印系統信息
    print_system_info()

    # 驗證系統需求
    if not validate_system_requirements():
        print("警告: 系統檢查未完全通過，實驗可能遇到問題")

    # 驗證實驗配置
    config_errors = validate_experiment_config(args)
    if config_errors:
        print("配置錯誤:")
        for error in config_errors:
            print(f"  - {error}")
        sys.exit(1)

    # 設置隨機種子
    global SEED
    SEED = args['seed']
    set_random_seeds(SEED)

    # 創建配置
    try:
        config = ManualConfig(
            aggregator_name=args['aggregator'],
            attack_name=args['attack'],
            dataset_name=args['dataset'],
            **args
        )
    except Exception as e:
        raise ConfigurationError(f"配置創建失敗: {str(e)}")

    # 驗證輸出目錄
    try:
        validate_output_directory(args['output_dir'])
    except Exception as e:
        raise ConfigurationError(f"輸出目錄驗證失敗: {str(e)}")

    # 打印配置摘要
    config.print_config_summary()

    # 獲取數據集加載器和分類器
    try:
        datasetLoader, classifier = get_dataset_loader_and_classifier(args['dataset'])
    except Exception as e:
        raise DatasetError(f"數據集加載器獲取失敗: {str(e)}")

    # 創建進度追蹤器
    progress_tracker = ProgressTracker(config.aggregatorConfig.rounds)

    # 運行實驗
    def run_experiment():
        logPrint("開始實驗...")
        progress_tracker.start()

        try:
            # 獲取聚合器類
            aggregator_class = config.get_aggregator_class()

            # 運行實驗（只運行一次，不遍歷所有場景）
            for attack_name in config.scenario_conversion():
                logPrint(f"運行實驗: {attack_name}")

                errors, attack_success_rate = run_single_experiment(
                    config=config,
                    datasetLoader=datasetLoader,
                    classifier=classifier,
                    agg=aggregator_class,
                    useDifferentialPrivacy=config.privacyPreserve or False,
                    folder=args['output_dir']
                )

                # 計算結果統計
                final_accuracy = float(errors[-1]) if len(errors) > 0 else 0.0
                final_attack_success_rate = float(attack_success_rate[-1]) if len(attack_success_rate) > 0 else 0.0
                avg_accuracy = float(torch.mean(errors)) if len(errors) > 0 else 0.0
                avg_attack_success_rate = float(torch.mean(attack_success_rate)) if len(attack_success_rate) > 0 else 0.0

                results = {
                    'final_accuracy': final_accuracy,
                    'final_attack_success_rate': final_attack_success_rate,
                    'avg_accuracy': avg_accuracy,
                    'avg_attack_success_rate': avg_attack_success_rate
                }

                # 保存結果
                save_results(errors, attack_success_rate, config, args['output_dir'])

                # 繪製圖表
                plot_results(errors, attack_success_rate, config, args['output_dir'])

                # 打印實驗摘要
                summary = create_experiment_summary(args, results)
                print(summary)

                break  # 只運行一個場景

        except Exception as e:
            raise AggregatorError(f"實驗執行失敗: {str(e)}")
        finally:
            # 清理內存
            memory_cleanup()
            progress_tracker.finish()

    # 安全執行實驗
    safe_experiment_execution(run_experiment)


if __name__ == "__main__":
    main()
