"""
錯誤處理和驗證工具模塊
"""

import os
import sys
import traceback
from typing import Any, Dict, List, Optional
from loguru import logger
import torch


class ExperimentError(Exception):
    """實驗相關的自定義異常"""
    pass


class ConfigurationError(ExperimentError):
    """配置相關的錯誤"""
    pass


class DatasetError(ExperimentError):
    """數據集相關的錯誤"""
    pass


class AggregatorError(ExperimentError):
    """聚合器相關的錯誤"""
    pass


def setup_error_logging(log_file: str = "experiment_errors.log"):
    """設置錯誤日誌記錄"""
    logger.add(
        log_file,
        level="ERROR",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
        rotation="10 MB",
        retention="7 days"
    )


def validate_system_requirements() -> bool:
    """驗證系統需求"""
    errors = []
    
    # 檢查 CUDA 可用性
    if not torch.cuda.is_available():
        errors.append("CUDA 不可用，實驗可能運行緩慢")
    else:
        gpu_count = torch.cuda.device_count()
        current_device = torch.cuda.current_device()
        gpu_name = torch.cuda.get_device_name(current_device)
        print(f"檢測到 {gpu_count} 個 GPU，當前使用: {gpu_name}")
    
    # 檢查內存
    if torch.cuda.is_available():
        gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        if gpu_memory < 4:  # 少於 4GB
            errors.append(f"GPU 內存較少 ({gpu_memory:.1f}GB)，可能影響大型模型訓練")
    
    # 檢查必要的目錄
    required_dirs = ['aggregators', 'classifiers', 'datasetLoaders', 'experiment', 'utils']
    for dir_name in required_dirs:
        if not os.path.exists(dir_name):
            errors.append(f"缺少必要目錄: {dir_name}")
    
    if errors:
        print("系統檢查警告:")
        for error in errors:
            print(f"  - {error}")
        return False
    
    print("系統檢查通過")
    return True


def validate_experiment_config(config: Dict[str, Any]) -> List[str]:
    """驗證實驗配置"""
    errors = []
    
    # 檢查必要參數
    required_params = ['aggregator', 'attack', 'dataset']
    for param in required_params:
        if param not in config or config[param] is None:
            errors.append(f"缺少必要參數: {param}")
    
    # 檢查數值參數範圍
    numeric_checks = {
        'rounds': (1, 1000, "輪數"),
        'epochs': (1, 100, "本地訓練輪數"),
        'batch_size': (1, 1024, "批次大小"),
        'learning_rate': (1e-6, 1.0, "學習率"),
        'alpha_dirichlet': (0.01, 10.0, "Alpha Dirichlet 參數")
    }
    
    for param, (min_val, max_val, desc) in numeric_checks.items():
        if param in config:
            value = config[param]
            if not isinstance(value, (int, float)) or value < min_val or value > max_val:
                errors.append(f"{desc} 必須在 {min_val} 到 {max_val} 之間")
    
    return errors


def safe_experiment_execution(func, *args, **kwargs):
    """安全執行實驗函數，包含錯誤處理"""
    try:
        return func(*args, **kwargs)
    except KeyboardInterrupt:
        print("\n實驗被用戶中斷")
        sys.exit(0)
    except torch.cuda.OutOfMemoryError as e:
        error_msg = f"GPU 內存不足: {str(e)}"
        logger.error(error_msg)
        print(f"錯誤: {error_msg}")
        print("建議: 減少批次大小或使用較小的模型")
        sys.exit(1)
    except FileNotFoundError as e:
        error_msg = f"文件未找到: {str(e)}"
        logger.error(error_msg)
        print(f"錯誤: {error_msg}")
        sys.exit(1)
    except ImportError as e:
        error_msg = f"模塊導入錯誤: {str(e)}"
        logger.error(error_msg)
        print(f"錯誤: {error_msg}")
        print("請檢查依賴是否正確安裝")
        sys.exit(1)
    except ConfigurationError as e:
        error_msg = f"配置錯誤: {str(e)}"
        logger.error(error_msg)
        print(f"錯誤: {error_msg}")
        sys.exit(1)
    except DatasetError as e:
        error_msg = f"數據集錯誤: {str(e)}"
        logger.error(error_msg)
        print(f"錯誤: {error_msg}")
        sys.exit(1)
    except AggregatorError as e:
        error_msg = f"聚合器錯誤: {str(e)}"
        logger.error(error_msg)
        print(f"錯誤: {error_msg}")
        sys.exit(1)
    except Exception as e:
        error_msg = f"未預期的錯誤: {str(e)}"
        logger.error(f"{error_msg}\n{traceback.format_exc()}")
        print(f"錯誤: {error_msg}")
        print("詳細錯誤信息已記錄到日誌文件")
        sys.exit(1)


def check_file_permissions(file_path: str) -> bool:
    """檢查文件權限"""
    try:
        # 檢查目錄是否存在，不存在則創建
        dir_path = os.path.dirname(file_path)
        if dir_path and not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)
        
        # 檢查寫入權限
        test_file = os.path.join(dir_path, '.test_write_permission')
        with open(test_file, 'w') as f:
            f.write('test')
        os.remove(test_file)
        return True
    except (OSError, IOError) as e:
        print(f"文件權限錯誤: {str(e)}")
        return False


def validate_output_directory(output_dir: str) -> bool:
    """驗證輸出目錄"""
    try:
        if not os.path.exists(output_dir):
            os.makedirs(output_dir, exist_ok=True)
            print(f"創建輸出目錄: {output_dir}")
        
        # 檢查寫入權限
        if not check_file_permissions(output_dir):
            raise ConfigurationError(f"無法寫入輸出目錄: {output_dir}")
        
        # 創建子目錄
        subdirs = ['json', 'graphs', 'logs']
        for subdir in subdirs:
            subdir_path = os.path.join(output_dir, subdir)
            if not os.path.exists(subdir_path):
                os.makedirs(subdir_path, exist_ok=True)
        
        return True
    except Exception as e:
        raise ConfigurationError(f"輸出目錄驗證失敗: {str(e)}")


def memory_cleanup():
    """清理內存"""
    import gc
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()


def print_system_info():
    """打印系統信息"""
    print("=== 系統信息 ===")
    print(f"Python 版本: {sys.version}")
    print(f"PyTorch 版本: {torch.__version__}")
    
    if torch.cuda.is_available():
        print(f"CUDA 版本: {torch.version.cuda}")
        print(f"GPU 數量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
            print(f"GPU {i}: {gpu_name} ({gpu_memory:.1f}GB)")
    else:
        print("CUDA: 不可用")
    
    print("=" * 20)


class ProgressTracker:
    """進度追蹤器"""
    
    def __init__(self, total_rounds: int):
        self.total_rounds = total_rounds
        self.current_round = 0
        self.start_time = None
    
    def start(self):
        """開始追蹤"""
        import time
        self.start_time = time.time()
        print(f"開始實驗，總共 {self.total_rounds} 輪")
    
    def update(self, round_num: int, accuracy: float = None, attack_success_rate: float = None):
        """更新進度"""
        import time
        self.current_round = round_num
        
        if self.start_time:
            elapsed = time.time() - self.start_time
            avg_time_per_round = elapsed / max(round_num, 1)
            remaining_rounds = self.total_rounds - round_num
            estimated_remaining = avg_time_per_round * remaining_rounds
            
            progress = (round_num / self.total_rounds) * 100
            
            status = f"進度: {progress:.1f}% ({round_num}/{self.total_rounds})"
            if accuracy is not None:
                status += f" | 準確率: {accuracy:.3f}"
            if attack_success_rate is not None:
                status += f" | 攻擊成功率: {attack_success_rate:.3f}"
            status += f" | 預計剩餘: {estimated_remaining/60:.1f}分鐘"
            
            print(f"\r{status}", end="", flush=True)
    
    def finish(self):
        """完成追蹤"""
        import time
        if self.start_time:
            total_time = time.time() - self.start_time
            print(f"\n實驗完成，總耗時: {total_time/60:.1f}分鐘")


def create_experiment_summary(config: Dict[str, Any], results: Dict[str, Any]) -> str:
    """創建實驗摘要"""
    summary = f"""
=== 實驗摘要 ===
聚合器: {config.get('aggregator', 'N/A')}
攻擊場景: {config.get('attack', 'N/A')}
數據集: {config.get('dataset', 'N/A')}
輪數: {config.get('rounds', 'N/A')}
本地訓練輪數: {config.get('epochs', 'N/A')}
批次大小: {config.get('batch_size', 'N/A')}
學習率: {config.get('learning_rate', 'N/A')}

=== 結果 ===
最終準確率: {results.get('final_accuracy', 'N/A'):.4f}
最終攻擊成功率: {results.get('final_attack_success_rate', 'N/A'):.4f}
平均準確率: {results.get('avg_accuracy', 'N/A'):.4f}
平均攻擊成功率: {results.get('avg_attack_success_rate', 'N/A'):.4f}
================
"""
    return summary
