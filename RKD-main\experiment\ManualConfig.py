import os
import torch
from typing import List, Dict, Any, Type
from experiment.DefaultExperimentConfiguration import DefaultExperimentConfiguration
from utils.typings import AttacksType, FreeRiderAttack
from aggregators.Aggregator import Aggregator

# 聚合器導入
from aggregators.FedAvg import FedAvgAggregator
from aggregators.RKD import RKDAggregator
from aggregators.FLAME import FLAMEAggregator
from aggregators.FedDF import FedDFAggregator
from aggregators.FedRAD import FedRADAggregator
from aggregators.FedBE import FedBEAggregator
from aggregators.RLR import RLRAggregator
from aggregators.Foolsgold import FoolsgoldAggregator
from aggregators.without_KD import without_KDAggregator


class ManualConfig(DefaultExperimentConfiguration):
    """
    手動配置類，支持單一攻擊方法和聚合方法的選擇
    """
    
    def __init__(self, 
                 aggregator_name: str = None,
                 attack_name: str = None,
                 dataset_name: str = 'cifar10',
                 **kwargs):
        super().__init__()
        
        # 聚合器映射
        self.aggregator_mapping = {
            'fedavg': FedAvgAggregator,
            'rkd': RKDAggregator,
            'flame': FLAMEAggregator,
            'feddf': FedDFAggregator,
            'fedrad': FedRADAggregator,
            'fedbe': FedBEAggregator,
            'rlr': RLRAggregator,
            'foolsgold': FoolsgoldAggregator,
            'without_kd': without_KDAggregator
        }
        
        # 攻擊場景映射
        self.attack_mapping = {
            'none': {
                'name': 'No Attacks',
                'faulty': [],
                'malicious': [],
                'free_rider': [],
                'attack_type': 'none'
            },
            # A3FL (A Little Is Enough) 攻擊
            'a3fl_20': {
                'name': '20% A3FL Attack',
                'faulty': [],
                'malicious': [],
                'free_rider': [2, 5, 8],
                'attack_type': 'a3fl'
            },
            'a3fl_40': {
                'name': '40% A3FL Attack',
                'faulty': [],
                'malicious': [],
                'free_rider': [2, 5, 8, 11, 14, 17],
                'attack_type': 'a3fl'
            },
            'a3fl_60': {
                'name': '60% A3FL Attack',
                'faulty': [],
                'malicious': [],
                'free_rider': [2, 5, 8, 11, 14, 17, 20, 23, 26],
                'attack_type': 'a3fl'
            },
            # DBA (Distributed Backdoor Attack) 攻擊
            'dba_20': {
                'name': '20% DBA Attack',
                'faulty': [],
                'malicious': [2, 5, 8],
                'free_rider': [],
                'attack_type': 'dba'
            },
            'dba_40': {
                'name': '40% DBA Attack',
                'faulty': [],
                'malicious': [2, 5, 8, 11, 14, 17],
                'free_rider': [],
                'attack_type': 'dba'
            },
            # F3BA (Federated Backdoor Attack) 攻擊
            'f3ba_20': {
                'name': '20% F3BA Attack',
                'faulty': [],
                'malicious': [2, 5, 8],
                'free_rider': [],
                'attack_type': 'f3ba'
            },
            'f3ba_40': {
                'name': '40% F3BA Attack',
                'faulty': [],
                'malicious': [2, 5, 8, 11, 14, 17],
                'free_rider': [],
                'attack_type': 'f3ba'
            },
            # TSBA (Trojan/Steganographic Backdoor Attack) 攻擊
            'tsba_20': {
                'name': '20% TSBA Attack',
                'faulty': [],
                'malicious': [2, 5, 8],
                'free_rider': [],
                'attack_type': 'tsba'
            },
            'tsba_40': {
                'name': '40% TSBA Attack',
                'faulty': [],
                'malicious': [2, 5, 8, 11, 14, 17],
                'free_rider': [],
                'attack_type': 'tsba'
            },
            # 拜占庭攻擊
            'byzantine_20': {
                'name': '20% Byzantine Attack',
                'faulty': [2, 5, 8],
                'malicious': [],
                'free_rider': [],
                'attack_type': 'byzantine'
            },
            'byzantine_40': {
                'name': '40% Byzantine Attack',
                'faulty': [2, 5, 8, 11, 14, 17],
                'malicious': [],
                'free_rider': [],
                'attack_type': 'byzantine'
            },
            # 混合攻擊
            'mixed_attack': {
                'name': 'Mixed Attack',
                'faulty': [2, 5],
                'malicious': [8, 11],
                'free_rider': [14, 17],
                'attack_type': 'mixed'
            }
        }
        
        # 設置基本配置
        self.nonIID = True
        self.alphaDirichlet = kwargs.get('alpha_dirichlet', 0.3)
        self.serverDataSize = 1.0 / 6
        self.privacyPreserve = False
        self.privacyAmplification = False
        
        # 更新實驗參數
        if 'rounds' in kwargs:
            self.aggregatorConfig.rounds = kwargs['rounds']
        if 'epochs' in kwargs:
            self.epochs = kwargs['epochs']
        if 'batch_size' in kwargs:
            self.batchSize = kwargs['batch_size']
        if 'learning_rate' in kwargs:
            self.learningRate = kwargs['learning_rate']
        if 'seed' in kwargs:
            global SEED
            SEED = kwargs['seed']
        
        # 設置繪圖選項
        self.plotResults = not kwargs.get('no_plot', False)
        
        # 設置輸出目錄
        self.output_dir = kwargs.get('output_dir', 'results')
        
        # 設置選定的聚合器和攻擊
        self.selected_aggregator = aggregator_name
        self.selected_attack = attack_name
        self.selected_dataset = dataset_name
        
        # 驗證選擇
        self._validate_selections()
        
        # 設置聚合器列表（只包含選定的聚合器）
        if self.selected_aggregator:
            self.aggregators = [self.aggregator_mapping[self.selected_aggregator]]
        else:
            self.aggregators = []
        
        # 設置攻擊場景
        if self.selected_attack:
            attack_config = self.attack_mapping[self.selected_attack]
            iid_string = f"non-IID alpha={self.alphaDirichlet}" if self.nonIID else "IID"
            
            self.scenarios: AttacksType = [
                (
                    attack_config['faulty'],
                    attack_config['malicious'], 
                    attack_config['free_rider'],
                    f"{attack_config['name']} {iid_string}"
                )
            ]
        else:
            self.scenarios = []
        
        # 設置用戶百分比
        self.percUsers = torch.tensor(PERC_USERS, device=self.aggregatorConfig.device)
    
    def _validate_selections(self):
        """驗證用戶選擇的有效性"""
        if self.selected_aggregator and self.selected_aggregator not in self.aggregator_mapping:
            raise ValueError(f"無效的聚合器: {self.selected_aggregator}")
        
        if self.selected_attack and self.selected_attack not in self.attack_mapping:
            raise ValueError(f"無效的攻擊場景: {self.selected_attack}")
    
    def get_aggregator_class(self) -> Type[Aggregator]:
        """獲取選定的聚合器類"""
        if not self.selected_aggregator:
            raise ValueError("未選擇聚合器")
        return self.aggregator_mapping[self.selected_aggregator]
    
    def get_attack_config(self) -> Dict[str, Any]:
        """獲取選定的攻擊配置"""
        if not self.selected_attack:
            raise ValueError("未選擇攻擊場景")
        return self.attack_mapping[self.selected_attack]
    
    def get_experiment_name(self) -> str:
        """獲取實驗名稱"""
        aggregator_name = self.selected_aggregator or "unknown"
        attack_name = self.selected_attack or "unknown"
        dataset_name = self.selected_dataset or "unknown"
        return f"{aggregator_name}_{attack_name}_{dataset_name}"
    
    def scenario_conversion(self):
        """
        設置故障、惡意和搭便車客戶端
        
        與原始版本兼容，但只返回單一場景
        """
        if not self.scenarios:
            return
            
        for faulty, malicious, freeRider, attackName in self.scenarios:
            self.faulty = faulty
            self.malicious = malicious
            self.freeRiding = freeRider
            self.name = attackName
            self.aggregatorConfig.attackName = attackName
            
            yield attackName
    
    def print_config_summary(self):
        """打印配置摘要"""
        print("=== 實驗配置摘要 ===")
        print(f"聚合器: {self.selected_aggregator}")
        print(f"攻擊場景: {self.selected_attack}")
        print(f"數據集: {self.selected_dataset}")
        print(f"輪數: {self.aggregatorConfig.rounds}")
        print(f"本地訓練輪數: {self.epochs}")
        print(f"批次大小: {self.batchSize}")
        print(f"學習率: {self.learningRate}")
        print(f"Alpha Dirichlet: {self.alphaDirichlet}")
        print(f"輸出目錄: {self.output_dir}")
        print(f"生成圖表: {self.plotResults}")
        
        if self.selected_attack != 'none':
            attack_config = self.get_attack_config()
            print(f"故障客戶端: {attack_config['faulty']}")
            print(f"惡意客戶端: {attack_config['malicious']}")
            print(f"搭便車客戶端: {attack_config['free_rider']}")
        print("=" * 25)


# 用戶數據分佈（與原始配置保持一致）
PERC_USERS: List[float] = [
    0.2, 0.15, 0.2, 0.2, 0.1, 0.15, 0.1, 0.15, 0.2, 0.2,
    0.2, 0.3, 0.2, 0.2, 0.1
]
