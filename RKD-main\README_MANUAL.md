# 聯邦學習實驗 - 手動選擇模式

本文檔說明如何使用手動選擇模式運行聯邦學習實驗，允許您選擇特定的攻擊方法和聚合方法組合，而不是運行所有可能的組合。

## 快速開始

### 基本使用

```bash
# 使用 RKD 聚合器和 20% A3FL 攻擊在 CIFAR-10 上運行實驗
python main_manual.py --aggregator rkd --attack a3fl_20 --dataset cifar10

# 使用 FedAvg 聚合器和無攻擊基線
python main_manual.py --aggregator fedavg --attack none --dataset cifar10
```

### 交互式模式

```bash
# 啟動交互式選擇模式
python main_manual.py --interactive
```

### 查看所有可用選項

```bash
# 列出所有可用的聚合器、攻擊場景和數據集
python main_manual.py --list-options
```

## 可用選項

### 聚合方法 (Aggregators)

| 選項 | 描述 | 論文/方法 |
|------|------|-----------|
| `fedavg` | FedAvg 聯邦平均 | 基礎聯邦學習方法 |
| `rkd` | RKD (Robust Knowledge Distillation) | 本項目的主要方法 |
| `flame` | FLAME | 聯邦學習異常檢測 |
| `feddf` | FedDF | 聯邦蒸餾框架 |
| `fedrad` | FedRAD | 魯棒聚合與蒸餾 |
| `fedbe` | FedBE | 拜占庭容錯聯邦學習 |
| `rlr` | RLR | 魯棒學習率 |
| `foolsgold` | FoolsGold | 基於歷史的拜占庭檢測 |
| `without_kd` | 無知識蒸餾版本 | 對比實驗用 |

### 攻擊場景 (Attack Scenarios)

#### 論文中的四種主要攻擊方法

| 選項 | 描述 | 攻擊類型 | 論文方法 |
|------|------|----------|----------|
| `none` | 無攻擊基線 | 正常聯邦學習 | - |
| `a3fl_20` | 20% A3FL 攻擊 | 20% 搭便車客戶端 | A Little Is Enough |
| `a3fl_40` | 40% A3FL 攻擊 | 40% 搭便車客戶端 | A Little Is Enough |
| `a3fl_60` | 60% A3FL 攻擊 | 60% 搭便車客戶端 | A Little Is Enough |
| `dba_20` | 20% DBA 攻擊 | 20% 分散式後門攻擊 | Distributed Backdoor Attack |
| `dba_40` | 40% DBA 攻擊 | 40% 分散式後門攻擊 | Distributed Backdoor Attack |
| `f3ba_20` | 20% F3BA 攻擊 | 20% 聯邦後門攻擊 | Federated Backdoor Attack |
| `f3ba_40` | 40% F3BA 攻擊 | 40% 聯邦後門攻擊 | Federated Backdoor Attack |
| `tsba_20` | 20% TSBA 攻擊 | 20% 特洛伊後門攻擊 | Trojan/Steganographic Backdoor Attack |
| `tsba_40` | 40% TSBA 攻擊 | 40% 特洛伊後門攻擊 | Trojan/Steganographic Backdoor Attack |

#### 其他攻擊方法

| 選項 | 描述 | 攻擊類型 |
|------|------|----------|
| `byzantine_20` | 20% 拜占庭攻擊 | 20% 故障客戶端 |
| `byzantine_40` | 40% 拜占庭攻擊 | 40% 故障客戶端 |
| `mixed_attack` | 混合攻擊 | 拜占庭+惡意+搭便車 |

### 數據集 (Datasets)

| 選項 | 描述 |
|------|------|
| `cifar10` | CIFAR-10 (默認) |
| `fashionmnist` | Fashion-MNIST |
| `emnist` | EMNIST |

## 命令行參數

### 必需參數

- `--aggregator, -a`: 選擇聚合方法
- `--attack, -t`: 選擇攻擊場景

### 可選參數

- `--dataset, -d`: 選擇數據集 (默認: cifar10)
- `--rounds, -r`: 聯邦學習輪數 (默認: 30)
- `--epochs, -e`: 本地訓練輪數 (默認: 5)
- `--batch-size, -b`: 批次大小 (默認: 64)
- `--learning-rate, -lr`: 學習率 (默認: 0.05)
- `--alpha-dirichlet`: Dirichlet 分佈參數 (默認: 0.3)
- `--output-dir, -o`: 結果輸出目錄 (默認: results)
- `--no-plot`: 不生成圖表
- `--seed`: 隨機種子 (默認: 0)

### 工具參數

- `--list-options`: 列出所有可用選項
- `--interactive, -i`: 交互式選擇模式

## 使用示例

### 1. 論文中的四種主要攻擊測試

```bash
# 測試 RKD 對 A3FL 攻擊的防禦效果
python main_manual.py --aggregator rkd --attack a3fl_20 --dataset cifar10

# 測試 RKD 對 DBA 攻擊的防禦效果
python main_manual.py --aggregator rkd --attack dba_20 --dataset cifar10

# 測試 RKD 對 F3BA 攻擊的防禦效果
python main_manual.py --aggregator rkd --attack f3ba_20 --dataset cifar10

# 測試 RKD 對 TSBA 攻擊的防禦效果
python main_manual.py --aggregator rkd --attack tsba_20 --dataset cifar10
```

### 2. 不同攻擊強度測試

```bash
# 測試不同強度的 A3FL 攻擊
python main_manual.py --aggregator rkd --attack a3fl_20 --dataset cifar10
python main_manual.py --aggregator rkd --attack a3fl_40 --dataset cifar10
python main_manual.py --aggregator rkd --attack a3fl_60 --dataset cifar10

# 測試不同強度的 DBA 攻擊
python main_manual.py --aggregator rkd --attack dba_20 --dataset cifar10
python main_manual.py --aggregator rkd --attack dba_40 --dataset cifar10
```

### 3. 基線對比實驗

```bash
# 對比 FedAvg 對各種攻擊的表現
python main_manual.py --aggregator fedavg --attack a3fl_20 --dataset cifar10
python main_manual.py --aggregator fedavg --attack dba_20 --dataset cifar10
python main_manual.py --aggregator fedavg --attack f3ba_20 --dataset cifar10
python main_manual.py --aggregator fedavg --attack tsba_20 --dataset cifar10
```

### 3. 不同數據集測試

```bash
# 在不同數據集上測試
python main_manual.py --aggregator rkd --attack a3fl_20 --dataset cifar10
python main_manual.py --aggregator rkd --attack a3fl_20 --dataset fashionmnist
python main_manual.py --aggregator rkd --attack a3fl_20 --dataset emnist
```

### 4. 自定義實驗參數

```bash
# 長時間實驗，更多輪數
python main_manual.py --aggregator rkd --attack a3fl_20 --dataset cifar10 \
    --rounds 100 --epochs 10 --learning-rate 0.01

# 快速測試，少輪數
python main_manual.py --aggregator rkd --attack none --dataset cifar10 \
    --rounds 10 --epochs 3 --batch-size 128
```

### 5. 批量實驗腳本

創建 `run_experiments.sh`:

```bash
#!/bin/bash

# 測試所有聚合器對 A3FL 攻擊的防禦效果
aggregators=("fedavg" "rkd" "flame" "feddf" "fedrad")
attacks=("none" "a3fl_20" "a3fl_40")

for agg in "${aggregators[@]}"; do
    for att in "${attacks[@]}"; do
        echo "Running experiment: $agg vs $att"
        python main_manual.py --aggregator $agg --attack $att --dataset cifar10 \
            --output-dir "results/${agg}_${att}" --no-plot
    done
done
```

## 輸出結果

實驗完成後，結果將保存在指定的輸出目錄中：

```
results/
├── json/
│   └── rkd_a3fl_20_cifar10_results(Seed_0).json
├── graphs/
│   ├── rkd_a3fl_20_cifar10_accuracy.pdf
│   └── rkd_a3fl_20_cifar10_attack_success_rate.pdf
└── logs/
    └── experiment_errors.log
```

### JSON 結果格式

```json
{
  "errors": [0.1, 0.15, 0.12, ...],
  "attack_success_rate": [0.8, 0.7, 0.6, ...],
  "config": {
    "aggregator": "rkd",
    "attack": "a3fl_20",
    "dataset": "cifar10",
    "rounds": 30,
    "epochs": 5,
    "batch_size": 64,
    "learning_rate": 0.05,
    "alpha_dirichlet": 0.3
  }
}
```

## 故障排除

### 常見錯誤

1. **CUDA 內存不足**
   ```bash
   # 減少批次大小
   python main_manual.py --aggregator rkd --attack a3fl_20 --batch-size 32
   ```

2. **無效的聚合器或攻擊名稱**
   ```bash
   # 查看可用選項
   python main_manual.py --list-options
   ```

3. **權限錯誤**
   ```bash
   # 指定有寫入權限的輸出目錄
   python main_manual.py --aggregator rkd --attack a3fl_20 --output-dir ~/results
   ```

### 系統需求

- Python 3.8+
- PyTorch 1.8+
- CUDA 支持 (推薦)
- 至少 4GB GPU 內存 (用於 CIFAR-10)

### 性能優化

1. **使用 GPU**: 確保 CUDA 可用
2. **調整批次大小**: 根據 GPU 內存調整
3. **減少輪數**: 用於快速測試
4. **禁用圖表生成**: 使用 `--no-plot` 加速

## 與原版本的差異

| 特性 | 原版本 | 手動選擇版本 |
|------|--------|--------------|
| 執行方式 | 自動遍歷所有組合 | 手動選擇單一組合 |
| 執行時間 | 長 (所有組合) | 短 (單一組合) |
| 資源使用 | 高 | 低 |
| 靈活性 | 低 | 高 |
| 適用場景 | 完整評估 | 特定測試 |

## 進階使用

### 自定義攻擊場景

如需添加新的攻擊場景，請修改 `argument_parser.py` 中的 `AVAILABLE_ATTACKS` 字典。

### 自定義聚合器

如需添加新的聚合器，請：
1. 在 `aggregators/` 目錄中實現新的聚合器類
2. 在 `argument_parser.py` 中添加到 `AVAILABLE_AGGREGATORS`
3. 在 `experiment/ManualConfig.py` 中添加映射

### 結果分析

使用提供的 JSON 結果文件，您可以：
1. 繪製自定義圖表
2. 進行統計分析
3. 比較不同實驗結果
4. 生成實驗報告
