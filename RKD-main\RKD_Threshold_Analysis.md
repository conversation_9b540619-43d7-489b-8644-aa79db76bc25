# RKD (Robust Knowledge Distillation) 閾值參數分析報告

## 概述

本報告詳細分析 RKD 論文實現中關於模型選擇門檻設定的各項參數，包括 k 值、閾值參數的具體設定和使用方式。

## 1. k 值和閾值參數的具體設定

### 1.1 HDBSCAN 聚類參數

**位置**: `RKDAggregator.identify_and_exclude_malicious_clients()` 方法

```python
# 動態調整 min_cluster_size
dynamic_min_cluster_size = max(2, int(num_clients * 0.2 - self.r))
clusterer = hdbscan.HDBSCAN(min_cluster_size=dynamic_min_cluster_size, metric='euclidean')
```

**參數說明**:
- **公式**: `max(2, int(num_clients * 0.2 - self.r))`
- **含義**: 最小聚類大小隨訓練輪數動態調整
- **初始值**: 當 `num_clients=15` 時，第 0 輪為 `max(2, int(15 * 0.2 - 0)) = 3`
- **動態變化**: 隨著輪數增加而減少，最小值為 2

### 1.2 模型選擇的 k 值

**位置**: `RKDAggregator.select_median_models_by_params()` 方法

```python
# 返回最接近中位數的前 k 個模型
return closest_models[:7]  # k = 7 (硬編碼)
```

**參數說明**:
- **k 值**: 固定為 7
- **用途**: 從良性客戶端模型中選擇最接近整體中位數的 7 個模型
- **選擇標準**: 基於模型參數中位數與整體中位數的距離

### 1.3 配置文件中的閾值參數

**位置**: `AggregatorConfig.py`

```python
# 聚類配置
self.cluster_count: int = 3
self.min_cluster_size = 4  # 靜態配置，但被動態值覆蓋
self.hdbscan_min_samples = 3
self.cluster_distance_threshold = 1.0
self.threshold: bool = True

# FedBE 參數（用於對比）
self.sampleSize = 15  # 採樣模型數量
```

## 2. 各算法步驟中的 k 值使用

### 2.1 惡意客戶端檢測流程

1. **餘弦相似度計算**
   - 無固定閾值，使用相對比較
   - 計算每個客戶端模型與平均模型的餘弦相似度

2. **HDBSCAN 聚類**
   - `min_cluster_size`: 動態調整，公式為 `max(2, int(num_clients * 0.2 - round))`
   - `metric`: 'euclidean'
   - `min_samples`: 使用默認值（未明確設定）

3. **良性客戶端識別**
   - 選擇具有最高平均餘弦相似度的聚類作為良性聚類
   - 無固定閾值，基於相對比較

### 2.2 模型選擇流程

1. **中位數計算**
   - 計算每個良性模型的參數中位數
   - 計算所有良性模型的整體中位數

2. **模型排序和選擇**
   - 按照與整體中位數的距離排序
   - 選擇前 k=7 個最接近的模型

## 3. k 值設定策略

### 3.1 動態調整策略

**HDBSCAN min_cluster_size 的動態調整**:

```python
dynamic_min_cluster_size = max(2, int(num_clients * 0.2 - self.r))
```

**調整邏輯**:
- 初期（早期輪數）：較大的聚類大小，更保守的聚類
- 後期（後期輪數）：較小的聚類大小，更靈活的聚類
- 最小值限制：不小於 2，確保聚類的有效性

**示例計算**（假設 15 個客戶端）:
- 第 0 輪: `max(2, int(15 * 0.2 - 0)) = max(2, 3) = 3`
- 第 1 輪: `max(2, int(15 * 0.2 - 1)) = max(2, 2) = 2`
- 第 2 輪: `max(2, int(15 * 0.2 - 2)) = max(2, 1) = 2`
- 第 3+ 輪: 保持為 2

### 3.2 固定參數

**模型選擇數量**: k = 7（硬編碼）
- 在 15 個客戶端的設定下，選擇約 47% 的良性模型
- 這個比例在不同客戶端數量下會有所變化

## 4. 不同攻擊場景下的參數一致性

### 4.1 參數統一性

根據代碼分析，**所有攻擊場景使用相同的 k 值設定**：
- A3FL 攻擊: 使用相同的聚類和選擇參數
- DBA 攻擊: 使用相同的聚類和選擇參數  
- F3BA 攻擊: 使用相同的聚類和選擇參數
- TSBA 攻擊: 使用相同的聚類和選擇參數
- Byzantine 攻擊: 使用相同的聚類和選擇參數

### 4.2 攻擊適應性

雖然參數設定相同，但算法的適應性體現在：
1. **動態聚類大小**: 根據訓練輪數自動調整
2. **相對閾值**: 基於當前輪次的模型分佈進行相對比較
3. **無絕對閾值**: 避免了固定閾值可能帶來的不適應性

## 5. 與論文的一致性分析

### 5.1 實現與論文的對應關係

**論文中的 k 值概念**:
- 在 RKD 論文中，k 通常指代選擇的模型數量或聚類參數
- 當前實現中的 k=7 對應於論文中的模型選擇數量

**聚類參數的對應**:
- 動態 `min_cluster_size` 對應論文中的自適應聚類策略
- 餘弦相似度閾值採用相對比較而非絕對閾值

### 5.2 潛在的改進空間

1. **參數可配置化**:
   ```python
   # 建議的改進
   self.model_selection_k = config.model_selection_k  # 默認 7
   self.cluster_size_ratio = config.cluster_size_ratio  # 默認 0.2
   ```

2. **攻擊自適應參數**:
   ```python
   # 根據攻擊類型調整參數
   if attack_type == 'byzantine':
       self.model_selection_k = 5  # 更保守
   elif attack_type in ['dba', 'f3ba', 'tsba']:
       self.model_selection_k = 8  # 更寬鬆
   ```

## 6. 關鍵代碼片段

### 6.1 動態聚類大小計算

```python
# RKD.py line 195-196
dynamic_min_cluster_size = max(2, int(num_clients * 0.2 - self.r))
print(f"dynamic_min_cluster_size-----: {dynamic_min_cluster_size}")
```

### 6.2 模型選擇

```python
# RKD.py line 274-278
closest_models = sorted(benign_models, 
    key=lambda m: abs(torch.cat([p.flatten() for p in m.parameters()]).median().item() - overall_median))
return closest_models[:7]  # k = 7
```

### 6.3 聚類配置

```python
# RKD.py line 201
clusterer = hdbscan.HDBSCAN(min_cluster_size=dynamic_min_cluster_size, metric='euclidean')
```

## 7. 總結

1. **k 值設定**: 模型選擇的 k=7 是硬編碼的，HDBSCAN 的 min_cluster_size 是動態調整的
2. **使用位置**: 主要用於 HDBSCAN 聚類和最終的模型選擇階段
3. **調整策略**: min_cluster_size 採用動態調整，模型選擇數量固定
4. **攻擊一致性**: 所有攻擊場景使用相同的參數設定
5. **論文一致性**: 基本與論文描述一致，但某些參數是硬編碼的
6. **改進建議**: 可以將硬編碼參數改為可配置參數，並考慮攻擊自適應調整

這種設計在保持算法穩定性的同時，通過動態調整提供了一定的適應性，但仍有進一步優化的空間。
